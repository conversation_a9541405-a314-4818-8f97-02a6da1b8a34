# 如何在main.go中调用libWorkWechatFinanceSdk_C.so中的函数

## 总结

我已经为你创建了完整的Go调用企业微信C动态库的解决方案。以下是关键要点：

## 核心文件

1. **wechat_sdk_wrapper.go** - 完整的SDK封装，包含所有主要功能
2. **example_simple.go** - 简单直接的调用示例
3. **main.go** - 主程序入口
4. **PLATFORM_COMPATIBILITY.md** - 平台兼容性详细说明

## 关键技术要点

### 1. CGO配置
```go
/*
#cgo linux CFLAGS: -I./wechat_sdk
#cgo linux LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#cgo darwin CFLAGS: -I./wechat_sdk  
#cgo darwin LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#include "WeWorkFinanceSdk_C.h"
#include <stdlib.h>
*/
import "C"
```

### 2. 基本调用流程
```go
// 1. 创建SDK实例
sdk := C.NewSdk()
defer C.DestroySdk(sdk)

// 2. 初始化
cCorpid := C.CString(corpid)
cSecret := C.CString(secret)
defer C.free(unsafe.Pointer(cCorpid))
defer C.free(unsafe.Pointer(cSecret))
ret := C.Init(sdk, cCorpid, cSecret)

// 3. 调用功能函数
slice := C.NewSlice()
defer C.FreeSlice(slice)
ret = C.GetChatData(sdk, seq, limit, proxy, passwd, timeout, slice)
```

### 3. 内存管理原则
- 使用`C.CString()`创建的字符串必须用`C.free()`释放
- 使用`C.NewSlice()`创建的结构体必须用`C.FreeSlice()`释放
- 使用`defer`确保资源正确释放

### 4. 类型转换
```go
// Go -> C
seq := C.ulonglong(0)
limit := C.uint(100)
timeout := C.int(10)

// C -> Go  
data := C.GoBytes(unsafe.Pointer(dataPtr), C.int(dataLen))
```

## 主要功能封装

### WechatSDK结构体
- `Init()` - 初始化SDK
- `GetChatData()` - 获取聊天记录
- `DecryptData()` - 解密聊天数据
- `GetMediaData()` - 获取媒体数据
- `Destroy()` - 销毁SDK实例

## 平台兼容性

**当前问题**: 提供的是Linux x86-64动态库，但运行环境是macOS ARM64

**解决方案**:
1. 在Linux环境下运行代码
2. 获取macOS版本的动态库
3. 使用Docker容器运行

## 编译和运行

### Linux环境下:
```bash
export CGO_ENABLED=1
export LD_LIBRARY_PATH=./wechat_sdk:$LD_LIBRARY_PATH
go build -o session-archive example_simple.go
./session-archive
```

### 当前macOS环境:
```bash
go build main.go  # 可以编译
./main           # 显示平台兼容性提示
```

## 错误处理

所有SDK函数都返回错误码，主要错误码含义：
- 0: 成功
- 10000: 参数错误
- 10001: 网络错误
- 10002: 数据解析失败
- 其他: 参考头文件中的错误码说明

## 使用建议

1. **开发阶段**: 使用`example_simple.go`理解基本调用方法
2. **生产环境**: 使用`wechat_sdk_wrapper.go`的封装版本
3. **测试**: 在Linux环境下进行完整测试
4. **部署**: 确保目标环境有正确的动态库文件

## 下一步

1. 获取适合你平台的动态库
2. 配置正确的企业微信参数(corpid, secret)
3. 在Linux环境下测试完整功能
4. 根据业务需求扩展功能

这个解决方案提供了完整的Go调用C动态库的框架，你可以根据实际需求进行调整和扩展。
