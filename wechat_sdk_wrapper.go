package main

/*
#cgo linux CFLAGS: -I./wechat_sdk
#cgo linux LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#cgo darwin CFLAGS: -I./wechat_sdk
#cgo darwin LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#include "WeWorkFinanceSdk_C.h"
#include <stdlib.h>
*/
import "C"
import (
	"unsafe"
)

// WechatSDK 封装企业微信SDK
type WechatSDK struct {
	sdk *C.WeWorkFinanceSdk_t
}

// NewWechatSDK 创建新的SDK实例
func NewWechatSDK() *WechatSDK {
	return &WechatSDK{
		sdk: C.NewSdk(),
	}
}

// Init 初始化SDK
func (w *WechatSDK) Init(corpid, secret string) int {
	cCorpid := C.CString(corpid)
	cSecret := C.CString(secret)
	defer C.free(unsafe.Pointer(cCorpid))
	defer C.free(unsafe.Pointer(cSecret))
	
	return int(C.Init(w.sdk, cCorpid, cSecret))
}

// GetChatData 获取聊天记录数据
func (w *WechatSDK) GetChatData(seq uint64, limit uint32, proxy, passwd string, timeout int) ([]byte, int) {
	cProxy := C.CString(proxy)
	cPasswd := C.CString(passwd)
	defer C.free(unsafe.Pointer(cProxy))
	defer C.free(unsafe.Pointer(cPasswd))
	
	slice := C.NewSlice()
	defer C.FreeSlice(slice)
	
	ret := int(C.GetChatData(w.sdk, C.ulonglong(seq), C.uint(limit), cProxy, cPasswd, C.int(timeout), slice))
	
	if ret != 0 {
		return nil, ret
	}
	
	// 获取数据
	dataPtr := C.GetContentFromSlice(slice)
	dataLen := int(C.GetSliceLen(slice))
	
	if dataLen > 0 {
		data := C.GoBytes(unsafe.Pointer(dataPtr), C.int(dataLen))
		return data, ret
	}
	
	return nil, ret
}

// DecryptData 解密聊天数据
func (w *WechatSDK) DecryptData(encryptKey, encryptMsg string) ([]byte, int) {
	cEncryptKey := C.CString(encryptKey)
	cEncryptMsg := C.CString(encryptMsg)
	defer C.free(unsafe.Pointer(cEncryptKey))
	defer C.free(unsafe.Pointer(cEncryptMsg))
	
	slice := C.NewSlice()
	defer C.FreeSlice(slice)
	
	ret := int(C.DecryptData(cEncryptKey, cEncryptMsg, slice))
	
	if ret != 0 {
		return nil, ret
	}
	
	// 获取解密后的数据
	dataPtr := C.GetContentFromSlice(slice)
	dataLen := int(C.GetSliceLen(slice))
	
	if dataLen > 0 {
		data := C.GoBytes(unsafe.Pointer(dataPtr), C.int(dataLen))
		return data, ret
	}
	
	return nil, ret
}

// GetMediaData 获取媒体数据
func (w *WechatSDK) GetMediaData(indexbuf, sdkFileid, proxy, passwd string, timeout int) (*MediaData, int) {
	cIndexbuf := C.CString(indexbuf)
	cSdkFileid := C.CString(sdkFileid)
	cProxy := C.CString(proxy)
	cPasswd := C.CString(passwd)
	defer C.free(unsafe.Pointer(cIndexbuf))
	defer C.free(unsafe.Pointer(cSdkFileid))
	defer C.free(unsafe.Pointer(cProxy))
	defer C.free(unsafe.Pointer(cPasswd))
	
	mediaData := C.NewMediaData()
	defer C.FreeMediaData(mediaData)
	
	ret := int(C.GetMediaData(w.sdk, cIndexbuf, cSdkFileid, cProxy, cPasswd, C.int(timeout), mediaData))
	
	if ret != 0 {
		return nil, ret
	}
	
	// 获取媒体数据
	outIndexPtr := C.GetOutIndexBuf(mediaData)
	dataPtr := C.GetData(mediaData)
	indexLen := int(C.GetIndexLen(mediaData))
	dataLen := int(C.GetDataLen(mediaData))
	isFinish := int(C.IsMediaDataFinish(mediaData))
	
	result := &MediaData{
		IsFinish: isFinish == 1,
	}
	
	if indexLen > 0 {
		result.OutIndexBuf = C.GoBytes(unsafe.Pointer(outIndexPtr), C.int(indexLen))
	}
	
	if dataLen > 0 {
		result.Data = C.GoBytes(unsafe.Pointer(dataPtr), C.int(dataLen))
	}
	
	return result, ret
}

// Destroy 销毁SDK实例
func (w *WechatSDK) Destroy() {
	if w.sdk != nil {
		C.DestroySdk(w.sdk)
		w.sdk = nil
	}
}

// MediaData 媒体数据结构
type MediaData struct {
	OutIndexBuf []byte
	Data        []byte
	IsFinish    bool
}
