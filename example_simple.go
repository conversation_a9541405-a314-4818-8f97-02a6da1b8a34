package main

/*
#cgo CFLAGS: -I./wechat_sdk
#cgo LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#include "WeWorkFinanceSdk_C.h"
#include <stdlib.h>
*/
import "C"
import (
	"fmt"
	"unsafe"
)

func main() {
	// 1. 创建SDK实例
	sdk := C.NewSdk()
	if sdk == nil {
		fmt.Println("创建SDK实例失败")
		return
	}
	defer C.DestroySdk(sdk)

	// 2. 初始化SDK
	corpid := "ww40cfe70c755de506"
	secret := "zNN2ZLkBsMxkbRp-mDRmnQ7ee3L-3NLtsaDtJkDAXeU"

	cCorpid := C.CString(corpid)
	cSecret := C.CString(secret)
	defer C.free(unsafe.Pointer(cCorpid))
	defer C.free(unsafe.Pointer(cSecret))

	ret := C.Init(sdk, cCorpid, cSecret)
	if ret != 0 {
		fmt.Printf("SDK初始化失败，错误码: %d\n", int(ret))
		return
	}

	fmt.Println("SDK初始化成功")

	// 3. 获取聊天数据
	seq := C.ulonglong(0)
	limit := C.uint(10)
	proxy := C.CString("")
	passwd := C.CString("")
	timeout := C.int(10)

	defer C.free(unsafe.Pointer(proxy))
	defer C.free(unsafe.Pointer(passwd))

	// 创建Slice来接收数据
	slice := C.NewSlice()
	defer C.FreeSlice(slice)

	ret = C.GetChatData(sdk, seq, limit, proxy, passwd, timeout, slice)
	if ret != 0 {
		fmt.Printf("获取聊天数据失败，错误码: %d\n", int(ret))
		return
	}

	// 获取返回的数据
	dataLen := C.GetSliceLen(slice)
	if dataLen > 0 {
		dataPtr := C.GetContentFromSlice(slice)
		data := C.GoBytes(unsafe.Pointer(dataPtr), dataLen)
		fmt.Printf("获取到聊天数据，长度: %d\n", int(dataLen))
		fmt.Printf("数据内容: %s\n", string(data))
	} else {
		fmt.Println("没有获取到聊天数据")
	}
}
