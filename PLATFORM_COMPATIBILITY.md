# 平台兼容性说明

## 当前状况

你的项目目录中包含的是Linux x86-64版本的动态库：
```
wechat_sdk/libWeWorkFinanceSdk_C.so: ELF 64-bit LSB shared object, x86-64, version 1 (GNU/Linux)
```

但你当前运行在macOS ARM64环境下，因此无法直接链接和运行。

## 解决方案

### 方案1：在Linux环境下运行

1. **使用Docker**
   ```bash
   # 创建Dockerfile
   FROM ubuntu:20.04
   RUN apt-get update && apt-get install -y golang-go gcc
   WORKDIR /app
   COPY . .
   RUN go build -o session-archive example_simple.go
   CMD ["./session-archive"]
   ```

2. **使用Linux虚拟机或服务器**
   - 将代码部署到Linux x86-64环境
   - 确保安装了Go和gcc
   - 运行编译和测试

### 方案2：获取macOS版本的动态库

需要从企业微信官方获取macOS版本的SDK，通常文件名为：
- `libWeWorkFinanceSdk_C.dylib` (macOS动态库)
- 或者 `libWeWorkFinanceSdk_C.a` (静态库)

### 方案3：交叉编译（不推荐）

由于涉及C动态库，交叉编译会比较复杂，不建议使用。

## 代码示例说明

尽管无法在当前环境下运行，但提供的代码示例展示了完整的调用方法：

### 1. 基本调用模式 (example_simple.go)

<augment_code_snippet path="example_simple.go" mode="EXCERPT">
````go
// 1. 创建SDK实例
sdk := C.NewSdk()
defer C.DestroySdk(sdk)

// 2. 初始化SDK
cCorpid := C.CString(corpid)
cSecret := C.CString(secret)
defer C.free(unsafe.Pointer(cCorpid))
defer C.free(unsafe.Pointer(cSecret))

ret := C.Init(sdk, cCorpid, cSecret)
````
</augment_code_snippet>

### 2. 封装模式 (wechat_sdk_wrapper.go)

<augment_code_snippet path="wechat_sdk_wrapper.go" mode="EXCERPT">
````go
// WechatSDK 封装企业微信SDK
type WechatSDK struct {
	sdk *C.WeWorkFinanceSdk_t
}

func (w *WechatSDK) Init(corpid, secret string) int {
	cCorpid := C.CString(corpid)
	cSecret := C.CString(secret)
	defer C.free(unsafe.Pointer(cCorpid))
	defer C.free(unsafe.Pointer(cSecret))
	
	return int(C.Init(w.sdk, cCorpid, cSecret))
}
````
</augment_code_snippet>

## 关键技术点

### CGO配置
```go
/*
#cgo linux CFLAGS: -I./wechat_sdk
#cgo linux LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#cgo darwin CFLAGS: -I./wechat_sdk
#cgo darwin LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#include "WeWorkFinanceSdk_C.h"
#include <stdlib.h>
*/
import "C"
```

### 内存管理
```go
// 创建C字符串
cStr := C.CString("hello")
defer C.free(unsafe.Pointer(cStr))

// 创建和释放C结构体
slice := C.NewSlice()
defer C.FreeSlice(slice)
```

### 类型转换
```go
// Go类型转C类型
seq := C.ulonglong(0)
limit := C.uint(100)
timeout := C.int(10)

// C数据转Go数据
data := C.GoBytes(unsafe.Pointer(dataPtr), C.int(dataLen))
```

## 测试建议

1. **在Linux环境下测试**
   ```bash
   export CGO_ENABLED=1
   export LD_LIBRARY_PATH=./wechat_sdk:$LD_LIBRARY_PATH
   go build -o session-archive example_simple.go
   ./session-archive
   ```

2. **验证动态库依赖**
   ```bash
   ldd ./session-archive
   ```

3. **调试CGO问题**
   ```bash
   CGO_ENABLED=1 go build -x example_simple.go
   ```

## 下一步

1. 获取适合你平台的动态库文件
2. 在Linux环境下测试代码
3. 根据实际的企业微信参数调整配置
4. 编写单元测试验证功能
