# 企业微信会话存档 Go SDK

这个项目展示了如何在Go中调用企业微信会话存档的C动态库。

## 项目结构

```
.
├── main.go                          # 主程序入口
├── example_simple.go                # 简单的调用示例
├── wechat_sdk_wrapper.go           # 完整的SDK封装
├── Makefile                         # 编译脚本
├── go.mod                          # Go模块文件
├── README.md                       # 说明文档
├── PLATFORM_COMPATIBILITY.md      # 平台兼容性说明
├── wechat_sdk/                     # 企业微信SDK
│   ├── WeWorkFinanceSdk_C.h        # C头文件
│   └── libWeWorkFinanceSdk_C.so    # C动态库(Linux x86-64)
```

## ⚠️ 重要提示

当前提供的动态库是Linux x86-64版本，如果你在macOS或其他平台上运行，需要获取对应平台的动态库文件。详细说明请查看 [PLATFORM_COMPATIBILITY.md](PLATFORM_COMPATIBILITY.md)。

## 核心概念

### CGO配置

在Go文件中使用CGO需要添加以下配置：

```go
/*
#cgo CFLAGS: -I./wechat_sdk
#cgo LDFLAGS: -L./wechat_sdk -lWeWorkFinanceSdk_C
#include "WeWorkFinanceSdk_C.h"
#include <stdlib.h>
*/
import "C"
```

- `CFLAGS`: 指定头文件路径
- `LDFLAGS`: 指定库文件路径和库名称
- `#include`: 包含C头文件

### 主要函数调用

1. **创建SDK实例**
   ```go
   sdk := C.NewSdk()
   defer C.DestroySdk(sdk)
   ```

2. **初始化SDK**
   ```go
   cCorpid := C.CString(corpid)
   cSecret := C.CString(secret)
   defer C.free(unsafe.Pointer(cCorpid))
   defer C.free(unsafe.Pointer(cSecret))
   
   ret := C.Init(sdk, cCorpid, cSecret)
   ```

3. **获取聊天数据**
   ```go
   slice := C.NewSlice()
   defer C.FreeSlice(slice)
   
   ret := C.GetChatData(sdk, seq, limit, proxy, passwd, timeout, slice)
   ```

4. **解密数据**
   ```go
   ret := C.DecryptData(cEncryptKey, cEncryptMsg, slice)
   ```

## 编译和运行

### 使用Makefile

```bash
# 编译主程序
make build

# 编译简单示例
make build-simple

# 运行主程序
make run

# 运行简单示例
make run-simple

# 测试编译
make test

# 清理编译产物
make clean
```

### 手动编译

```bash
# 设置环境变量
export CGO_ENABLED=1
export LD_LIBRARY_PATH=./wechat_sdk:$LD_LIBRARY_PATH

# 编译
go build -o session-archive main.go

# 运行
./session-archive
```

## 重要注意事项

1. **内存管理**: 使用`C.CString()`创建的C字符串需要手动释放
   ```go
   cStr := C.CString("hello")
   defer C.free(unsafe.Pointer(cStr))
   ```

2. **类型转换**: Go类型需要转换为对应的C类型
   ```go
   seq := C.ulonglong(0)
   limit := C.uint(100)
   timeout := C.int(10)
   ```

3. **资源释放**: C分配的资源需要手动释放
   ```go
   slice := C.NewSlice()
   defer C.FreeSlice(slice)
   ```

4. **动态库路径**: 确保运行时能找到动态库
   ```bash
   export LD_LIBRARY_PATH=./wechat_sdk:$LD_LIBRARY_PATH
   ```

## 错误码说明

- 10000: 参数错误
- 10001: 网络错误
- 10002: 数据解析失败
- 10003: 系统失败
- 10004: 密钥错误，重新下载失败
- 10005: fileid错误
- 10006: 解密失败
- 10007: 找不到消息加密版本的私钥，需要重新传入私钥
- 10008: 解析encrypt_key错误
- 10009: ip非法
- 10010: 数据过期
- 10011: 证书错误

## 使用示例

在使用前，请确保：

1. 替换`corpid`和`secret`为实际的企业微信参数
2. 确保动态库文件存在且可访问
3. 设置正确的环境变量

```go
// 替换为实际的企业微信参数
corpid := "wwd08c8exxxx5ab44d"
secret := "your_actual_secret_here"
```
