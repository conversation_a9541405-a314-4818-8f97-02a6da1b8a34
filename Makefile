# Makefile for WeChat Session Archive

# 设置环境变量
export CGO_ENABLED=1

# 默认目标
.PHONY: all clean build run test

all: build

# 编译主程序
build:
	go build -o session-archive main.go

# 编译简单示例
build-simple:
	go build -o example-simple example_simple.go

# 运行主程序
run: build
	./session-archive

# 运行简单示例
run-simple: build-simple
	./example-simple

# 测试编译
test:
	go build -v main.go

# 清理编译产物
clean:
	rm -f session-archive example-simple

# 检查动态库依赖
check-deps:
	ldd ./session-archive 2>/dev/null || otool -L ./session-archive 2>/dev/null || echo "请先编译程序"

# 显示帮助信息
help:
	@echo "可用的命令:"
	@echo "  make build        - 编译主程序"
	@echo "  make build-simple - 编译简单示例"
	@echo "  make run          - 运行主程序"
	@echo "  make run-simple   - 运行简单示例"
	@echo "  make test         - 测试编译"
	@echo "  make clean        - 清理编译产物"
	@echo "  make check-deps   - 检查动态库依赖"
	@echo "  make help         - 显示此帮助信息"
